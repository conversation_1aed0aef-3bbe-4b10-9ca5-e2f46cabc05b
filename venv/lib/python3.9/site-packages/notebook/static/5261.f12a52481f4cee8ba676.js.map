{"version": 3, "file": "5261.f12a52481f4cee8ba676.js?v=f12a52481f4cee8ba676", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,sBAAsB;AAC/B;AACA;;AAEA;AACA;AACA;AACA,IAAI;AACJ,IAAI;AACJ;AACA,IAAI;AACJ,oBAAoB,kBAAkB;AACtC,IAAI;AACJ;AACA;AACA;;;AC3B6G;AACqG;AAC1L;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB;AACvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,8BAAW;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,sBAAsB,gCAAa;AACnC,8BAA8B,gCAAa;AAC3C;AACA;AACA,8CAA8C;AAC9C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,yBAAyB;AACjD;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0BAA0B;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4DAA4D;;AAE5D,gBAAgB;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,yBAAyB;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA,gBAAgB,KAAG,YAAY,kDAAkD;AACjF,cAAc,KAAG;AACjB;AACA;AACA,uCAAuC;AACvC;AACA,gCAAgC,iCAAiC;AACjE;AACA;AACA,4CAA4C;AAC5C;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK,EAAE,KAAG,8DAA8D,KAAG,aAAa,oCAAoC,4BAA4B,KAAG;AAC3J;AACA;AACA,4BAA4B,iCAAiC;AAC7D;AACA,SAAS;AACT;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,wBAAwB,8BAAe;AACvC;AACA,8CAA8C,oBAAU,kCAAkC,aAAa;AACvG;AACA,SAAS;AACT;AACA;AACA,aAAa;AACb;AACA,kCAAkC,0BAAW;AAC7C,iCAAiC,yBAAU;AAC3C,eAAe,cAAc;AAC7B;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,kBAAkB,mBAAS;AAC3B,CAAC;AACD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,sBAAQ;AACxB;AACA;AACA;AACA,yBAAyB,0BAAW;AACpC,wBAAwB,SAAS;AACjC,gBAAgB,sBAAQ;AACxB;AACA;AACA;AACA;AACA;AACA,iCAAiC,oBAAU;AAC3C;AACA;AACA;AACA,qBAAqB,iBAAiB;AACtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,oBAAK;AAC1C;AACA,eAAe,gCAAa;AAC5B;AACA;AACA;AACA,SAAS;AACT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,oBAAU,QAAQ,4BAA4B;AAC7E,mCAAmC,oBAAU,QAAQ,mDAAmD;AACxG;AACA;AACA,kEAAkE,2BAAY;AAC9E,wEAAwE,2BAAY;AACpF;AACA;AACA;AACA,oDAAoD,2BAAY;AAChE,gDAAgD,2BAAY;AAC5D;AACA,sCAAsC,oBAAU;AAChD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,cAAc,QAAQ;AACtB;AACA,mBAAmB,oBAAU;AAC7B;AACA;AACA;AACA,uBAAuB,oBAAU;AACjC;AACA;AACA,uBAAuB,oBAAU;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,oBAAU;AACjC;AACA,8DAA8D;AAC9D;AACA;AACA;AACA,2BAA2B,oBAAU;AACrC;AACA;AACA;AACA;AACA,2BAA2B,oBAAU;AACrC;AACA;AACA;AACA;AACA;AACA;AACA,sBAAsB,WAAW;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,oBAAU;AACzC;AACA;AACA;AACA,eAAe,oBAAU;AACzB;AACA,CAAC;AACD;AACA,CAAC;AACD,kCAAkC,oBAAU;AAC5C,4BAA4B,8BAA8B;AAC1D,4CAA4C;AAC5C,CAAC;AACD;AACA,sBAAsB,iBAAiB;AACvC,UAAU,YAAY;AACtB,iBAAiB,8BAAe,kEAAkE,8BAAe;AACjH;AACA;AACA,4BAA4B,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA,UAAU,eAAe;AACzB;AACA,wGAAwG;AACxG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,iBAAiB;AACjD,UAAU,SAAS;AACnB;AACA,4BAA4B,iBAAiB;AAC7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4CAA4C,8BAAe;AAC3D,iBAAiB,oBAAU;AAC3B,KAAK;AACL;AACA;;AAEA,uCAAuC,oBAAK;AAC5C;AACA,eAAe,gCAAa;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,oBAAU;AAC9C,SAAS;AACT;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qCAAqC,0BAAW,UAAU,YAAY;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D,2BAAY;AAC3E,0DAA0D,2BAAY;AACtE,yDAAyD,2BAAY;AACrE,6DAA6D,2BAAY;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B;AAC3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8BAA8B;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,qBAAqB,mCAAgB;AACrC;AACA;AACA,4BAA4B,mCAAgB;AAC5C;AACA;AACA;AACA,8DAA8D,2BAAY;AAC1E,gEAAgE,2BAAY;AAC5E,mFAAmF,2BAAY;AAC/F,uFAAuF,2BAAY;AACnG;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,4BAA4B;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mCAAmC,OAAO;AAC1C;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oCAAoC,0BAAW;AAC/C,iCAAiC,0BAAW;AAC5C,iCAAiC,yBAAU;AAC3C;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL,kBAAkB,mBAAS;AAC3B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+BAA+B,oBAAU,QAAQ,yBAAyB,oCAAoC,oBAAU,QAAQ,iDAAiD;AACjL,uCAAuC,oBAAU;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,cAAc;AAC9B;AACA,mBAAmB,oBAAU;AAC7B,cAAc,OAAO;AACrB,0BAA0B,8BAAe;AACzC,wEAAwE,OAAO;AAC/E,kBAAkB,WAAW;AAC7B;AACA;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA,CAAC;AACD;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qDAAqD,OAAO;AAC5D,UAAU,KAAK;AACf;AACA;AACA;AACA,oBAAoB,8BAAe;AACnC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,yDAAyD,OAAO;AAChE,UAAU,QAAQ,UAAU,OAAO;AACnC;AACA;AACA;AACA,oBAAoB,8BAAe;AACnC;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,CAAC;AACD;AACA;AACA;AACA,0DAA0D,OAAO;AACjE;AACA;AACA;AACA;AACA,mBAAmB,8BAAe,wBAAwB,8BAAe;AACzE;AACA,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA,kCAAkC,iBAAiB;AACnD;AACA;AACA;AACA,UAAU,WAAW;AACrB;AACA,0EAA0E,iBAAiB;AAC3F;AACA;AACA;AACA;AACA,oBAAoB,8BAAe;AACnC;AACA;AACA,mBAAmB,8BAAe;AAClC;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,wDAAwD,OAAO;AAC/D,UAAU,QAAQ,UAAU,WAAW;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,uBAAuB,mDAAmD;AAC1E;AACA,qBAAqB,oBAAU;AAC/B;AACA;AACA;AACA,oBAAoB,8BAAe;AACnC;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA,uDAAuD,OAAO;AAC9D;AACA;AACA;AACA,cAAc,WAAW;AACzB,iBAAiB;AACjB,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,iBAAiB,oBAAU;AAC3B;AACA,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,gBAAgB,sBAAQ;AACxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gCAAgC,mCAAmC;AACnE;AACA;AACA;AACA;AACA;AACA,wBAAwB;AACxB;AACA,wFAAwF,0BAAW;AACnG,eAAe;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB,sBAAQ;AACxB;AACA;AACA,oBAAoB,gCAAgC;AACpD;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM,kEAAkE;AACxE,MAAM,mGAAmG;AACzG,MAAM,sGAAsG;AAC5G,MAAM,oEAAoE;AAC1E,MAAM,iDAAiD;AACvD,MAAM,iCAAiC;AACvC,MAAM,+DAA+D;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,2BAA2B,KAAG;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,4BAA4B,KAAG;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,yBAAyB,KAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,uBAAuB,KAAG;AAC1B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT,yBAAyB,KAAG;AAC5B;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA,mBAAmB,KAAG,aAAa,mDAAmD;AACtF;AACA,mBAAmB,KAAG,UAAU,uDAAuD;AACvF;AACA;AACA;AACA;AACA,YAAY,KAAG;AACf,YAAY,KAAG;AACf,YAAY,KAAG;AACf;AACA,gBAAgB,KAAG;AACnB;AACA;AACA;AACA;AACA,YAAY,KAAG;AACf;AACA;AACA;AACA;AACA,aAAa;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,iCAAiC,mCAAmC;AACpE;AACA;AACA;AACA,YAAY,8BAAgB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gBAAgB;AAChB,gBAAgB;AAChB;AACA,gCAAgC;AAChC;AACA,uBAAuB;AACvB,+BAA+B,UAAU;AACzC;AACA;AACA;AACA;AACA,wBAAwB,oBAAoB;AAC5C;AACA;AACA;AACA;AACA;AACA;AACA,sCAAsC,kCAAkC;AACxE;AACA;AACA;AACA;AACA;AACA,WAAW,oBAAU,gBAAgB,mCAAmC,IAAI,MAAM,EAAE,8BAA8B,EAAE,YAAY;AAChI;AACA,+BAA+B,oBAAU;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,KAAK;AACL,gCAAgC,8BAA8B;AAC9D,+BAA+B,8BAA8B;AAC7D,yCAAyC,8BAA8B;AACvE,wCAAwC;AACxC,CAAC;AACD;AACA;AACA,iBAAiB,mBAAI;AACrB;AACA;;AAEgT", "sources": ["webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/crelt/index.js", "webpack://_JUPYTERLAB.CORE_OUTPUT/../node_modules/@codemirror/search/dist/index.js"], "sourcesContent": ["export default function crelt() {\n  var elt = arguments[0]\n  if (typeof elt == \"string\") elt = document.createElement(elt)\n  var i = 1, next = arguments[1]\n  if (next && typeof next == \"object\" && next.nodeType == null && !Array.isArray(next)) {\n    for (var name in next) if (Object.prototype.hasOwnProperty.call(next, name)) {\n      var value = next[name]\n      if (typeof value == \"string\") elt.setAttribute(name, value)\n      else if (value != null) elt[name] = value\n    }\n    i++\n  }\n  for (; i < arguments.length; i++) add(elt, arguments[i])\n  return elt\n}\n\nfunction add(elt, child) {\n  if (typeof child == \"string\") {\n    elt.appendChild(document.createTextNode(child))\n  } else if (child == null) {\n  } else if (child.nodeType != null) {\n    elt.appendChild(child)\n  } else if (Array.isArray(child)) {\n    for (var i = 0; i < child.length; i++) add(elt, child[i])\n  } else {\n    throw new RangeError(\"Unsupported child node: \" + child)\n  }\n}\n", "import { show<PERSON><PERSON>l, Editor<PERSON>iew, getPanel, Decoration, ViewPlugin, runScopeHandlers } from '@codemirror/view';\nimport { codePointAt, fromCodePoint, codePointSize, StateEffect, StateField, EditorSelection, Facet, combineConfig, CharCategory, RangeSetBuilder, Prec, EditorState, findClusterBreak } from '@codemirror/state';\nimport elt from 'crelt';\n\nconst basicNormalize = typeof String.prototype.normalize == \"function\"\n    ? x => x.normalize(\"NFKD\") : x => x;\n/**\nA search cursor provides an iterator over text matches in a\ndocument.\n*/\nclass SearchCursor {\n    /**\n    Create a text cursor. The query is the search string, `from` to\n    `to` provides the region to search.\n    \n    When `normalize` is given, it will be called, on both the query\n    string and the content it is matched against, before comparing.\n    You can, for example, create a case-insensitive search by\n    passing `s => s.toLowerCase()`.\n    \n    Text is always normalized with\n    [`.normalize(\"NFKD\")`](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/normalize)\n    (when supported).\n    */\n    constructor(text, query, from = 0, to = text.length, normalize, test) {\n        this.test = test;\n        /**\n        The current match (only holds a meaningful value after\n        [`next`](https://codemirror.net/6/docs/ref/#search.SearchCursor.next) has been called and when\n        `done` is false).\n        */\n        this.value = { from: 0, to: 0 };\n        /**\n        Whether the end of the iterated region has been reached.\n        */\n        this.done = false;\n        this.matches = [];\n        this.buffer = \"\";\n        this.bufferPos = 0;\n        this.iter = text.iterRange(from, to);\n        this.bufferStart = from;\n        this.normalize = normalize ? x => normalize(basicNormalize(x)) : basicNormalize;\n        this.query = this.normalize(query);\n    }\n    peek() {\n        if (this.bufferPos == this.buffer.length) {\n            this.bufferStart += this.buffer.length;\n            this.iter.next();\n            if (this.iter.done)\n                return -1;\n            this.bufferPos = 0;\n            this.buffer = this.iter.value;\n        }\n        return codePointAt(this.buffer, this.bufferPos);\n    }\n    /**\n    Look for the next match. Updates the iterator's\n    [`value`](https://codemirror.net/6/docs/ref/#search.SearchCursor.value) and\n    [`done`](https://codemirror.net/6/docs/ref/#search.SearchCursor.done) properties. Should be called\n    at least once before using the cursor.\n    */\n    next() {\n        while (this.matches.length)\n            this.matches.pop();\n        return this.nextOverlapping();\n    }\n    /**\n    The `next` method will ignore matches that partially overlap a\n    previous match. This method behaves like `next`, but includes\n    such matches.\n    */\n    nextOverlapping() {\n        for (;;) {\n            let next = this.peek();\n            if (next < 0) {\n                this.done = true;\n                return this;\n            }\n            let str = fromCodePoint(next), start = this.bufferStart + this.bufferPos;\n            this.bufferPos += codePointSize(next);\n            let norm = this.normalize(str);\n            if (norm.length)\n                for (let i = 0, pos = start;; i++) {\n                    let code = norm.charCodeAt(i);\n                    let match = this.match(code, pos, this.bufferPos + this.bufferStart);\n                    if (i == norm.length - 1) {\n                        if (match) {\n                            this.value = match;\n                            return this;\n                        }\n                        break;\n                    }\n                    if (pos == start && i < str.length && str.charCodeAt(i) == code)\n                        pos++;\n                }\n        }\n    }\n    match(code, pos, end) {\n        let match = null;\n        for (let i = 0; i < this.matches.length; i += 2) {\n            let index = this.matches[i], keep = false;\n            if (this.query.charCodeAt(index) == code) {\n                if (index == this.query.length - 1) {\n                    match = { from: this.matches[i + 1], to: end };\n                }\n                else {\n                    this.matches[i]++;\n                    keep = true;\n                }\n            }\n            if (!keep) {\n                this.matches.splice(i, 2);\n                i -= 2;\n            }\n        }\n        if (this.query.charCodeAt(0) == code) {\n            if (this.query.length == 1)\n                match = { from: pos, to: end };\n            else\n                this.matches.push(1, pos);\n        }\n        if (match && this.test && !this.test(match.from, match.to, this.buffer, this.bufferStart))\n            match = null;\n        return match;\n    }\n}\nif (typeof Symbol != \"undefined\")\n    SearchCursor.prototype[Symbol.iterator] = function () { return this; };\n\nconst empty = { from: -1, to: -1, match: /*@__PURE__*//.*/.exec(\"\") };\nconst baseFlags = \"gm\" + (/x/.unicode == null ? \"\" : \"u\");\n/**\nThis class is similar to [`SearchCursor`](https://codemirror.net/6/docs/ref/#search.SearchCursor)\nbut searches for a regular expression pattern instead of a plain\nstring.\n*/\nclass RegExpCursor {\n    /**\n    Create a cursor that will search the given range in the given\n    document. `query` should be the raw pattern (as you'd pass it to\n    `new RegExp`).\n    */\n    constructor(text, query, options, from = 0, to = text.length) {\n        this.text = text;\n        this.to = to;\n        this.curLine = \"\";\n        /**\n        Set to `true` when the cursor has reached the end of the search\n        range.\n        */\n        this.done = false;\n        /**\n        Will contain an object with the extent of the match and the\n        match object when [`next`](https://codemirror.net/6/docs/ref/#search.RegExpCursor.next)\n        sucessfully finds a match.\n        */\n        this.value = empty;\n        if (/\\\\[sWDnr]|\\n|\\r|\\[\\^/.test(query))\n            return new MultilineRegExpCursor(text, query, options, from, to);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.iter = text.iter();\n        let startLine = text.lineAt(from);\n        this.curLineStart = startLine.from;\n        this.matchPos = toCharEnd(text, from);\n        this.getLine(this.curLineStart);\n    }\n    getLine(skip) {\n        this.iter.next(skip);\n        if (this.iter.lineBreak) {\n            this.curLine = \"\";\n        }\n        else {\n            this.curLine = this.iter.value;\n            if (this.curLineStart + this.curLine.length > this.to)\n                this.curLine = this.curLine.slice(0, this.to - this.curLineStart);\n            this.iter.next();\n        }\n    }\n    nextLine() {\n        this.curLineStart = this.curLineStart + this.curLine.length + 1;\n        if (this.curLineStart > this.to)\n            this.curLine = \"\";\n        else\n            this.getLine(0);\n    }\n    /**\n    Move to the next match, if there is one.\n    */\n    next() {\n        for (let off = this.matchPos - this.curLineStart;;) {\n            this.re.lastIndex = off;\n            let match = this.matchPos <= this.to && this.re.exec(this.curLine);\n            if (match) {\n                let from = this.curLineStart + match.index, to = from + match[0].length;\n                this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                if (from == this.curLineStart + this.curLine.length)\n                    this.nextLine();\n                if ((from < to || from > this.value.to) && (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    return this;\n                }\n                off = this.matchPos - this.curLineStart;\n            }\n            else if (this.curLineStart + this.curLine.length < this.to) {\n                this.nextLine();\n                off = 0;\n            }\n            else {\n                this.done = true;\n                return this;\n            }\n        }\n    }\n}\nconst flattened = /*@__PURE__*/new WeakMap();\n// Reusable (partially) flattened document strings\nclass FlattenedDoc {\n    constructor(from, text) {\n        this.from = from;\n        this.text = text;\n    }\n    get to() { return this.from + this.text.length; }\n    static get(doc, from, to) {\n        let cached = flattened.get(doc);\n        if (!cached || cached.from >= to || cached.to <= from) {\n            let flat = new FlattenedDoc(from, doc.sliceString(from, to));\n            flattened.set(doc, flat);\n            return flat;\n        }\n        if (cached.from == from && cached.to == to)\n            return cached;\n        let { text, from: cachedFrom } = cached;\n        if (cachedFrom > from) {\n            text = doc.sliceString(from, cachedFrom) + text;\n            cachedFrom = from;\n        }\n        if (cached.to < to)\n            text += doc.sliceString(cached.to, to);\n        flattened.set(doc, new FlattenedDoc(cachedFrom, text));\n        return new FlattenedDoc(from, text.slice(from - cachedFrom, to - cachedFrom));\n    }\n}\nclass MultilineRegExpCursor {\n    constructor(text, query, options, from, to) {\n        this.text = text;\n        this.to = to;\n        this.done = false;\n        this.value = empty;\n        this.matchPos = toCharEnd(text, from);\n        this.re = new RegExp(query, baseFlags + ((options === null || options === void 0 ? void 0 : options.ignoreCase) ? \"i\" : \"\"));\n        this.test = options === null || options === void 0 ? void 0 : options.test;\n        this.flat = FlattenedDoc.get(text, from, this.chunkEnd(from + 5000 /* Chunk.Base */));\n    }\n    chunkEnd(pos) {\n        return pos >= this.to ? this.to : this.text.lineAt(pos).to;\n    }\n    next() {\n        for (;;) {\n            let off = this.re.lastIndex = this.matchPos - this.flat.from;\n            let match = this.re.exec(this.flat.text);\n            // Skip empty matches directly after the last match\n            if (match && !match[0] && match.index == off) {\n                this.re.lastIndex = off + 1;\n                match = this.re.exec(this.flat.text);\n            }\n            if (match) {\n                let from = this.flat.from + match.index, to = from + match[0].length;\n                // If a match goes almost to the end of a noncomplete chunk, try\n                // again, since it'll likely be able to match more\n                if ((this.flat.to >= this.to || match.index + match[0].length <= this.flat.text.length - 10) &&\n                    (!this.test || this.test(from, to, match))) {\n                    this.value = { from, to, match };\n                    this.matchPos = toCharEnd(this.text, to + (from == to ? 1 : 0));\n                    return this;\n                }\n            }\n            if (this.flat.to == this.to) {\n                this.done = true;\n                return this;\n            }\n            // Grow the flattened doc\n            this.flat = FlattenedDoc.get(this.text, this.flat.from, this.chunkEnd(this.flat.from + this.flat.text.length * 2));\n        }\n    }\n}\nif (typeof Symbol != \"undefined\") {\n    RegExpCursor.prototype[Symbol.iterator] = MultilineRegExpCursor.prototype[Symbol.iterator] =\n        function () { return this; };\n}\nfunction validRegExp(source) {\n    try {\n        new RegExp(source, baseFlags);\n        return true;\n    }\n    catch (_a) {\n        return false;\n    }\n}\nfunction toCharEnd(text, pos) {\n    if (pos >= text.length)\n        return pos;\n    let line = text.lineAt(pos), next;\n    while (pos < line.to && (next = line.text.charCodeAt(pos - line.from)) >= 0xDC00 && next < 0xE000)\n        pos++;\n    return pos;\n}\n\nfunction createLineDialog(view) {\n    let line = String(view.state.doc.lineAt(view.state.selection.main.head).number);\n    let input = elt(\"input\", { class: \"cm-textfield\", name: \"line\", value: line });\n    let dom = elt(\"form\", {\n        class: \"cm-gotoLine\",\n        onkeydown: (event) => {\n            if (event.keyCode == 27) { // Escape\n                event.preventDefault();\n                view.dispatch({ effects: dialogEffect.of(false) });\n                view.focus();\n            }\n            else if (event.keyCode == 13) { // Enter\n                event.preventDefault();\n                go();\n            }\n        },\n        onsubmit: (event) => {\n            event.preventDefault();\n            go();\n        }\n    }, elt(\"label\", view.state.phrase(\"Go to line\"), \": \", input), \" \", elt(\"button\", { class: \"cm-button\", type: \"submit\" }, view.state.phrase(\"go\")), elt(\"button\", {\n        name: \"close\",\n        onclick: () => {\n            view.dispatch({ effects: dialogEffect.of(false) });\n            view.focus();\n        },\n        \"aria-label\": view.state.phrase(\"close\"),\n        type: \"button\"\n    }, [\"×\"]));\n    function go() {\n        let match = /^([+-])?(\\d+)?(:\\d+)?(%)?$/.exec(input.value);\n        if (!match)\n            return;\n        let { state } = view, startLine = state.doc.lineAt(state.selection.main.head);\n        let [, sign, ln, cl, percent] = match;\n        let col = cl ? +cl.slice(1) : 0;\n        let line = ln ? +ln : startLine.number;\n        if (ln && percent) {\n            let pc = line / 100;\n            if (sign)\n                pc = pc * (sign == \"-\" ? -1 : 1) + (startLine.number / state.doc.lines);\n            line = Math.round(state.doc.lines * pc);\n        }\n        else if (ln && sign) {\n            line = line * (sign == \"-\" ? -1 : 1) + startLine.number;\n        }\n        let docLine = state.doc.line(Math.max(1, Math.min(state.doc.lines, line)));\n        let selection = EditorSelection.cursor(docLine.from + Math.max(0, Math.min(col, docLine.length)));\n        view.dispatch({\n            effects: [dialogEffect.of(false), EditorView.scrollIntoView(selection.from, { y: 'center' })],\n            selection,\n        });\n        view.focus();\n    }\n    return { dom };\n}\nconst dialogEffect = /*@__PURE__*/StateEffect.define();\nconst dialogField = /*@__PURE__*/StateField.define({\n    create() { return true; },\n    update(value, tr) {\n        for (let e of tr.effects)\n            if (e.is(dialogEffect))\n                value = e.value;\n        return value;\n    },\n    provide: f => showPanel.from(f, val => val ? createLineDialog : null)\n});\n/**\nCommand that shows a dialog asking the user for a line number, and\nwhen a valid position is provided, moves the cursor to that line.\n\nSupports line numbers, relative line offsets prefixed with `+` or\n`-`, document percentages suffixed with `%`, and an optional\ncolumn position by adding `:` and a second number after the line\nnumber.\n*/\nconst gotoLine = view => {\n    let panel = getPanel(view, createLineDialog);\n    if (!panel) {\n        let effects = [dialogEffect.of(true)];\n        if (view.state.field(dialogField, false) == null)\n            effects.push(StateEffect.appendConfig.of([dialogField, baseTheme$1]));\n        view.dispatch({ effects });\n        panel = getPanel(view, createLineDialog);\n    }\n    if (panel)\n        panel.dom.querySelector(\"input\").select();\n    return true;\n};\nconst baseTheme$1 = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-panel.cm-gotoLine\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& label\": { fontSize: \"80%\" },\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\", bottom: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: \"0\"\n        }\n    }\n});\n\nconst defaultHighlightOptions = {\n    highlightWordAroundCursor: false,\n    minSelectionLength: 1,\n    maxMatches: 100,\n    wholeWords: false\n};\nconst highlightConfig = /*@__PURE__*/Facet.define({\n    combine(options) {\n        return combineConfig(options, defaultHighlightOptions, {\n            highlightWordAroundCursor: (a, b) => a || b,\n            minSelectionLength: Math.min,\n            maxMatches: Math.min\n        });\n    }\n});\n/**\nThis extension highlights text that matches the selection. It uses\nthe `\"cm-selectionMatch\"` class for the highlighting. When\n`highlightWordAroundCursor` is enabled, the word at the cursor\nitself will be highlighted with `\"cm-selectionMatch-main\"`.\n*/\nfunction highlightSelectionMatches(options) {\n    let ext = [defaultTheme, matchHighlighter];\n    if (options)\n        ext.push(highlightConfig.of(options));\n    return ext;\n}\nconst matchDeco = /*@__PURE__*/Decoration.mark({ class: \"cm-selectionMatch\" });\nconst mainMatchDeco = /*@__PURE__*/Decoration.mark({ class: \"cm-selectionMatch cm-selectionMatch-main\" });\n// Whether the characters directly outside the given positions are non-word characters\nfunction insideWordBoundaries(check, state, from, to) {\n    return (from == 0 || check(state.sliceDoc(from - 1, from)) != CharCategory.Word) &&\n        (to == state.doc.length || check(state.sliceDoc(to, to + 1)) != CharCategory.Word);\n}\n// Whether the characters directly at the given positions are word characters\nfunction insideWord(check, state, from, to) {\n    return check(state.sliceDoc(from, from + 1)) == CharCategory.Word\n        && check(state.sliceDoc(to - 1, to)) == CharCategory.Word;\n}\nconst matchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.decorations = this.getDeco(view);\n    }\n    update(update) {\n        if (update.selectionSet || update.docChanged || update.viewportChanged)\n            this.decorations = this.getDeco(update.view);\n    }\n    getDeco(view) {\n        let conf = view.state.facet(highlightConfig);\n        let { state } = view, sel = state.selection;\n        if (sel.ranges.length > 1)\n            return Decoration.none;\n        let range = sel.main, query, check = null;\n        if (range.empty) {\n            if (!conf.highlightWordAroundCursor)\n                return Decoration.none;\n            let word = state.wordAt(range.head);\n            if (!word)\n                return Decoration.none;\n            check = state.charCategorizer(range.head);\n            query = state.sliceDoc(word.from, word.to);\n        }\n        else {\n            let len = range.to - range.from;\n            if (len < conf.minSelectionLength || len > 200)\n                return Decoration.none;\n            if (conf.wholeWords) {\n                query = state.sliceDoc(range.from, range.to); // TODO: allow and include leading/trailing space?\n                check = state.charCategorizer(range.head);\n                if (!(insideWordBoundaries(check, state, range.from, range.to) &&\n                    insideWord(check, state, range.from, range.to)))\n                    return Decoration.none;\n            }\n            else {\n                query = state.sliceDoc(range.from, range.to);\n                if (!query)\n                    return Decoration.none;\n            }\n        }\n        let deco = [];\n        for (let part of view.visibleRanges) {\n            let cursor = new SearchCursor(state.doc, query, part.from, part.to);\n            while (!cursor.next().done) {\n                let { from, to } = cursor.value;\n                if (!check || insideWordBoundaries(check, state, from, to)) {\n                    if (range.empty && from <= range.from && to >= range.to)\n                        deco.push(mainMatchDeco.range(from, to));\n                    else if (from >= range.to || to <= range.from)\n                        deco.push(matchDeco.range(from, to));\n                    if (deco.length > conf.maxMatches)\n                        return Decoration.none;\n                }\n            }\n        }\n        return Decoration.set(deco);\n    }\n}, {\n    decorations: v => v.decorations\n});\nconst defaultTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-selectionMatch\": { backgroundColor: \"#99ff7780\" },\n    \".cm-searchMatch .cm-selectionMatch\": { backgroundColor: \"transparent\" }\n});\n// Select the words around the cursors.\nconst selectWord = ({ state, dispatch }) => {\n    let { selection } = state;\n    let newSel = EditorSelection.create(selection.ranges.map(range => state.wordAt(range.head) || EditorSelection.cursor(range.head)), selection.mainIndex);\n    if (newSel.eq(selection))\n        return false;\n    dispatch(state.update({ selection: newSel }));\n    return true;\n};\n// Find next occurrence of query relative to last cursor. Wrap around\n// the document if there are no more matches.\nfunction findNextOccurrence(state, query) {\n    let { main, ranges } = state.selection;\n    let word = state.wordAt(main.head), fullWord = word && word.from == main.from && word.to == main.to;\n    for (let cycled = false, cursor = new SearchCursor(state.doc, query, ranges[ranges.length - 1].to);;) {\n        cursor.next();\n        if (cursor.done) {\n            if (cycled)\n                return null;\n            cursor = new SearchCursor(state.doc, query, 0, Math.max(0, ranges[ranges.length - 1].from - 1));\n            cycled = true;\n        }\n        else {\n            if (cycled && ranges.some(r => r.from == cursor.value.from))\n                continue;\n            if (fullWord) {\n                let word = state.wordAt(cursor.value.from);\n                if (!word || word.from != cursor.value.from || word.to != cursor.value.to)\n                    continue;\n            }\n            return cursor.value;\n        }\n    }\n}\n/**\nSelect next occurrence of the current selection. Expand selection\nto the surrounding word when the selection is empty.\n*/\nconst selectNextOccurrence = ({ state, dispatch }) => {\n    let { ranges } = state.selection;\n    if (ranges.some(sel => sel.from === sel.to))\n        return selectWord({ state, dispatch });\n    let searchedText = state.sliceDoc(ranges[0].from, ranges[0].to);\n    if (state.selection.ranges.some(r => state.sliceDoc(r.from, r.to) != searchedText))\n        return false;\n    let range = findNextOccurrence(state, searchedText);\n    if (!range)\n        return false;\n    dispatch(state.update({\n        selection: state.selection.addRange(EditorSelection.range(range.from, range.to), false),\n        effects: EditorView.scrollIntoView(range.to)\n    }));\n    return true;\n};\n\nconst searchConfigFacet = /*@__PURE__*/Facet.define({\n    combine(configs) {\n        return combineConfig(configs, {\n            top: false,\n            caseSensitive: false,\n            literal: false,\n            regexp: false,\n            wholeWord: false,\n            createPanel: view => new SearchPanel(view),\n            scrollToMatch: range => EditorView.scrollIntoView(range)\n        });\n    }\n});\n/**\nAdd search state to the editor configuration, and optionally\nconfigure the search extension.\n([`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) will automatically\nenable this if it isn't already on).\n*/\nfunction search(config) {\n    return config ? [searchConfigFacet.of(config), searchExtensions] : searchExtensions;\n}\n/**\nA search query. Part of the editor's search state.\n*/\nclass SearchQuery {\n    /**\n    Create a query object.\n    */\n    constructor(config) {\n        this.search = config.search;\n        this.caseSensitive = !!config.caseSensitive;\n        this.literal = !!config.literal;\n        this.regexp = !!config.regexp;\n        this.replace = config.replace || \"\";\n        this.valid = !!this.search && (!this.regexp || validRegExp(this.search));\n        this.unquoted = this.unquote(this.search);\n        this.wholeWord = !!config.wholeWord;\n    }\n    /**\n    @internal\n    */\n    unquote(text) {\n        return this.literal ? text :\n            text.replace(/\\\\([nrt\\\\])/g, (_, ch) => ch == \"n\" ? \"\\n\" : ch == \"r\" ? \"\\r\" : ch == \"t\" ? \"\\t\" : \"\\\\\");\n    }\n    /**\n    Compare this query to another query.\n    */\n    eq(other) {\n        return this.search == other.search && this.replace == other.replace &&\n            this.caseSensitive == other.caseSensitive && this.regexp == other.regexp &&\n            this.wholeWord == other.wholeWord;\n    }\n    /**\n    @internal\n    */\n    create() {\n        return this.regexp ? new RegExpQuery(this) : new StringQuery(this);\n    }\n    /**\n    Get a search cursor for this query, searching through the given\n    range in the given state.\n    */\n    getCursor(state, from = 0, to) {\n        let st = state.doc ? state : EditorState.create({ doc: state });\n        if (to == null)\n            to = st.doc.length;\n        return this.regexp ? regexpCursor(this, st, from, to) : stringCursor(this, st, from, to);\n    }\n}\nclass QueryType {\n    constructor(spec) {\n        this.spec = spec;\n    }\n}\nfunction stringCursor(spec, state, from, to) {\n    return new SearchCursor(state.doc, spec.unquoted, from, to, spec.caseSensitive ? undefined : x => x.toLowerCase(), spec.wholeWord ? stringWordTest(state.doc, state.charCategorizer(state.selection.main.head)) : undefined);\n}\nfunction stringWordTest(doc, categorizer) {\n    return (from, to, buf, bufPos) => {\n        if (bufPos > from || bufPos + buf.length < to) {\n            bufPos = Math.max(0, from - 2);\n            buf = doc.sliceString(bufPos, Math.min(doc.length, to + 2));\n        }\n        return (categorizer(charBefore(buf, from - bufPos)) != CharCategory.Word ||\n            categorizer(charAfter(buf, from - bufPos)) != CharCategory.Word) &&\n            (categorizer(charAfter(buf, to - bufPos)) != CharCategory.Word ||\n                categorizer(charBefore(buf, to - bufPos)) != CharCategory.Word);\n    };\n}\nclass StringQuery extends QueryType {\n    constructor(spec) {\n        super(spec);\n    }\n    nextMatch(state, curFrom, curTo) {\n        let cursor = stringCursor(this.spec, state, curTo, state.doc.length).nextOverlapping();\n        if (cursor.done) {\n            let end = Math.min(state.doc.length, curFrom + this.spec.unquoted.length);\n            cursor = stringCursor(this.spec, state, 0, end).nextOverlapping();\n        }\n        return cursor.done || cursor.value.from == curFrom && cursor.value.to == curTo ? null : cursor.value;\n    }\n    // Searching in reverse is, rather than implementing an inverted search\n    // cursor, done by scanning chunk after chunk forward.\n    prevMatchInRange(state, from, to) {\n        for (let pos = to;;) {\n            let start = Math.max(from, pos - 10000 /* FindPrev.ChunkSize */ - this.spec.unquoted.length);\n            let cursor = stringCursor(this.spec, state, start, pos), range = null;\n            while (!cursor.nextOverlapping().done)\n                range = cursor.value;\n            if (range)\n                return range;\n            if (start == from)\n                return null;\n            pos -= 10000 /* FindPrev.ChunkSize */;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        let found = this.prevMatchInRange(state, 0, curFrom);\n        if (!found)\n            found = this.prevMatchInRange(state, Math.max(0, curTo - this.spec.unquoted.length), state.doc.length);\n        return found && (found.from != curFrom || found.to != curTo) ? found : null;\n    }\n    getReplacement(_result) { return this.spec.unquote(this.spec.replace); }\n    matchAll(state, limit) {\n        let cursor = stringCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = stringCursor(this.spec, state, Math.max(0, from - this.spec.unquoted.length), Math.min(to + this.spec.unquoted.length, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\nfunction regexpCursor(spec, state, from, to) {\n    return new RegExpCursor(state.doc, spec.search, {\n        ignoreCase: !spec.caseSensitive,\n        test: spec.wholeWord ? regexpWordTest(state.charCategorizer(state.selection.main.head)) : undefined\n    }, from, to);\n}\nfunction charBefore(str, index) {\n    return str.slice(findClusterBreak(str, index, false), index);\n}\nfunction charAfter(str, index) {\n    return str.slice(index, findClusterBreak(str, index));\n}\nfunction regexpWordTest(categorizer) {\n    return (_from, _to, match) => !match[0].length ||\n        (categorizer(charBefore(match.input, match.index)) != CharCategory.Word ||\n            categorizer(charAfter(match.input, match.index)) != CharCategory.Word) &&\n            (categorizer(charAfter(match.input, match.index + match[0].length)) != CharCategory.Word ||\n                categorizer(charBefore(match.input, match.index + match[0].length)) != CharCategory.Word);\n}\nclass RegExpQuery extends QueryType {\n    nextMatch(state, curFrom, curTo) {\n        let cursor = regexpCursor(this.spec, state, curTo, state.doc.length).next();\n        if (cursor.done)\n            cursor = regexpCursor(this.spec, state, 0, curFrom).next();\n        return cursor.done ? null : cursor.value;\n    }\n    prevMatchInRange(state, from, to) {\n        for (let size = 1;; size++) {\n            let start = Math.max(from, to - size * 10000 /* FindPrev.ChunkSize */);\n            let cursor = regexpCursor(this.spec, state, start, to), range = null;\n            while (!cursor.next().done)\n                range = cursor.value;\n            if (range && (start == from || range.from > start + 10))\n                return range;\n            if (start == from)\n                return null;\n        }\n    }\n    prevMatch(state, curFrom, curTo) {\n        return this.prevMatchInRange(state, 0, curFrom) ||\n            this.prevMatchInRange(state, curTo, state.doc.length);\n    }\n    getReplacement(result) {\n        return this.spec.unquote(this.spec.replace).replace(/\\$([$&]|\\d+)/g, (m, i) => {\n            if (i == \"&\")\n                return result.match[0];\n            if (i == \"$\")\n                return \"$\";\n            for (let l = i.length; l > 0; l--) {\n                let n = +i.slice(0, l);\n                if (n > 0 && n < result.match.length)\n                    return result.match[n] + i.slice(l);\n            }\n            return m;\n        });\n    }\n    matchAll(state, limit) {\n        let cursor = regexpCursor(this.spec, state, 0, state.doc.length), ranges = [];\n        while (!cursor.next().done) {\n            if (ranges.length >= limit)\n                return null;\n            ranges.push(cursor.value);\n        }\n        return ranges;\n    }\n    highlight(state, from, to, add) {\n        let cursor = regexpCursor(this.spec, state, Math.max(0, from - 250 /* RegExp.HighlightMargin */), Math.min(to + 250 /* RegExp.HighlightMargin */, state.doc.length));\n        while (!cursor.next().done)\n            add(cursor.value.from, cursor.value.to);\n    }\n}\n/**\nA state effect that updates the current search query. Note that\nthis only has an effect if the search state has been initialized\n(by including [`search`](https://codemirror.net/6/docs/ref/#search.search) in your configuration or\nby running [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel) at least\nonce).\n*/\nconst setSearchQuery = /*@__PURE__*/StateEffect.define();\nconst togglePanel = /*@__PURE__*/StateEffect.define();\nconst searchState = /*@__PURE__*/StateField.define({\n    create(state) {\n        return new SearchState(defaultQuery(state).create(), null);\n    },\n    update(value, tr) {\n        for (let effect of tr.effects) {\n            if (effect.is(setSearchQuery))\n                value = new SearchState(effect.value.create(), value.panel);\n            else if (effect.is(togglePanel))\n                value = new SearchState(value.query, effect.value ? createSearchPanel : null);\n        }\n        return value;\n    },\n    provide: f => showPanel.from(f, val => val.panel)\n});\n/**\nGet the current search query from an editor state.\n*/\nfunction getSearchQuery(state) {\n    let curState = state.field(searchState, false);\n    return curState ? curState.query.spec : defaultQuery(state);\n}\n/**\nQuery whether the search panel is open in the given editor state.\n*/\nfunction searchPanelOpen(state) {\n    var _a;\n    return ((_a = state.field(searchState, false)) === null || _a === void 0 ? void 0 : _a.panel) != null;\n}\nclass SearchState {\n    constructor(query, panel) {\n        this.query = query;\n        this.panel = panel;\n    }\n}\nconst matchMark = /*@__PURE__*/Decoration.mark({ class: \"cm-searchMatch\" }), selectedMatchMark = /*@__PURE__*/Decoration.mark({ class: \"cm-searchMatch cm-searchMatch-selected\" });\nconst searchHighlighter = /*@__PURE__*/ViewPlugin.fromClass(class {\n    constructor(view) {\n        this.view = view;\n        this.decorations = this.highlight(view.state.field(searchState));\n    }\n    update(update) {\n        let state = update.state.field(searchState);\n        if (state != update.startState.field(searchState) || update.docChanged || update.selectionSet || update.viewportChanged)\n            this.decorations = this.highlight(state);\n    }\n    highlight({ query, panel }) {\n        if (!panel || !query.spec.valid)\n            return Decoration.none;\n        let { view } = this;\n        let builder = new RangeSetBuilder();\n        for (let i = 0, ranges = view.visibleRanges, l = ranges.length; i < l; i++) {\n            let { from, to } = ranges[i];\n            while (i < l - 1 && to > ranges[i + 1].from - 2 * 250 /* RegExp.HighlightMargin */)\n                to = ranges[++i].to;\n            query.highlight(view.state, from, to, (from, to) => {\n                let selected = view.state.selection.ranges.some(r => r.from == from && r.to == to);\n                builder.add(from, to, selected ? selectedMatchMark : matchMark);\n            });\n        }\n        return builder.finish();\n    }\n}, {\n    decorations: v => v.decorations\n});\nfunction searchCommand(f) {\n    return view => {\n        let state = view.state.field(searchState, false);\n        return state && state.query.spec.valid ? f(view, state) : openSearchPanel(view);\n    };\n}\n/**\nOpen the search panel if it isn't already open, and move the\nselection to the first match after the current main selection.\nWill wrap around to the start of the document when it reaches the\nend.\n*/\nconst findNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { to } = view.state.selection.main;\n    let next = query.nextMatch(view.state, to, to);\n    if (!next)\n        return false;\n    let selection = EditorSelection.single(next.from, next.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, next), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nMove the selection to the previous instance of the search query,\nbefore the current main selection. Will wrap past the start\nof the document to start searching at the end again.\n*/\nconst findPrevious = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from } = state.selection.main;\n    let prev = query.prevMatch(state, from, from);\n    if (!prev)\n        return false;\n    let selection = EditorSelection.single(prev.from, prev.to);\n    let config = view.state.facet(searchConfigFacet);\n    view.dispatch({\n        selection,\n        effects: [announceMatch(view, prev), config.scrollToMatch(selection.main, view)],\n        userEvent: \"select.search\"\n    });\n    selectSearchInput(view);\n    return true;\n});\n/**\nSelect all instances of the search query.\n*/\nconst selectMatches = /*@__PURE__*/searchCommand((view, { query }) => {\n    let ranges = query.matchAll(view.state, 1000);\n    if (!ranges || !ranges.length)\n        return false;\n    view.dispatch({\n        selection: EditorSelection.create(ranges.map(r => EditorSelection.range(r.from, r.to))),\n        userEvent: \"select.search.matches\"\n    });\n    return true;\n});\n/**\nSelect all instances of the currently selected text.\n*/\nconst selectSelectionMatches = ({ state, dispatch }) => {\n    let sel = state.selection;\n    if (sel.ranges.length > 1 || sel.main.empty)\n        return false;\n    let { from, to } = sel.main;\n    let ranges = [], main = 0;\n    for (let cur = new SearchCursor(state.doc, state.sliceDoc(from, to)); !cur.next().done;) {\n        if (ranges.length > 1000)\n            return false;\n        if (cur.value.from == from)\n            main = ranges.length;\n        ranges.push(EditorSelection.range(cur.value.from, cur.value.to));\n    }\n    dispatch(state.update({\n        selection: EditorSelection.create(ranges, main),\n        userEvent: \"select.search.matches\"\n    }));\n    return true;\n};\n/**\nReplace the current match of the search query.\n*/\nconst replaceNext = /*@__PURE__*/searchCommand((view, { query }) => {\n    let { state } = view, { from, to } = state.selection.main;\n    if (state.readOnly)\n        return false;\n    let match = query.nextMatch(state, from, from);\n    if (!match)\n        return false;\n    let next = match;\n    let changes = [], selection, replacement;\n    let effects = [];\n    if (next.from == from && next.to == to) {\n        replacement = state.toText(query.getReplacement(next));\n        changes.push({ from: next.from, to: next.to, insert: replacement });\n        next = query.nextMatch(state, next.from, next.to);\n        effects.push(EditorView.announce.of(state.phrase(\"replaced match on line $\", state.doc.lineAt(from).number) + \".\"));\n    }\n    if (next) {\n        let off = changes.length == 0 || changes[0].from >= match.to ? 0 : match.to - match.from - replacement.length;\n        selection = EditorSelection.single(next.from - off, next.to - off);\n        effects.push(announceMatch(view, next));\n        effects.push(state.facet(searchConfigFacet).scrollToMatch(selection.main, view));\n    }\n    view.dispatch({\n        changes, selection, effects,\n        userEvent: \"input.replace\"\n    });\n    return true;\n});\n/**\nReplace all instances of the search query with the given\nreplacement.\n*/\nconst replaceAll = /*@__PURE__*/searchCommand((view, { query }) => {\n    if (view.state.readOnly)\n        return false;\n    let changes = query.matchAll(view.state, 1e9).map(match => {\n        let { from, to } = match;\n        return { from, to, insert: query.getReplacement(match) };\n    });\n    if (!changes.length)\n        return false;\n    let announceText = view.state.phrase(\"replaced $ matches\", changes.length) + \".\";\n    view.dispatch({\n        changes,\n        effects: EditorView.announce.of(announceText),\n        userEvent: \"input.replace.all\"\n    });\n    return true;\n});\nfunction createSearchPanel(view) {\n    return view.state.facet(searchConfigFacet).createPanel(view);\n}\nfunction defaultQuery(state, fallback) {\n    var _a, _b, _c, _d, _e;\n    let sel = state.selection.main;\n    let selText = sel.empty || sel.to > sel.from + 100 ? \"\" : state.sliceDoc(sel.from, sel.to);\n    if (fallback && !selText)\n        return fallback;\n    let config = state.facet(searchConfigFacet);\n    return new SearchQuery({\n        search: ((_a = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _a !== void 0 ? _a : config.literal) ? selText : selText.replace(/\\n/g, \"\\\\n\"),\n        caseSensitive: (_b = fallback === null || fallback === void 0 ? void 0 : fallback.caseSensitive) !== null && _b !== void 0 ? _b : config.caseSensitive,\n        literal: (_c = fallback === null || fallback === void 0 ? void 0 : fallback.literal) !== null && _c !== void 0 ? _c : config.literal,\n        regexp: (_d = fallback === null || fallback === void 0 ? void 0 : fallback.regexp) !== null && _d !== void 0 ? _d : config.regexp,\n        wholeWord: (_e = fallback === null || fallback === void 0 ? void 0 : fallback.wholeWord) !== null && _e !== void 0 ? _e : config.wholeWord\n    });\n}\nfunction getSearchInput(view) {\n    let panel = getPanel(view, createSearchPanel);\n    return panel && panel.dom.querySelector(\"[main-field]\");\n}\nfunction selectSearchInput(view) {\n    let input = getSearchInput(view);\n    if (input && input == view.root.activeElement)\n        input.select();\n}\n/**\nMake sure the search panel is open and focused.\n*/\nconst openSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (state && state.panel) {\n        let searchInput = getSearchInput(view);\n        if (searchInput && searchInput != view.root.activeElement) {\n            let query = defaultQuery(view.state, state.query.spec);\n            if (query.valid)\n                view.dispatch({ effects: setSearchQuery.of(query) });\n            searchInput.focus();\n            searchInput.select();\n        }\n    }\n    else {\n        view.dispatch({ effects: [\n                togglePanel.of(true),\n                state ? setSearchQuery.of(defaultQuery(view.state, state.query.spec)) : StateEffect.appendConfig.of(searchExtensions)\n            ] });\n    }\n    return true;\n};\n/**\nClose the search panel.\n*/\nconst closeSearchPanel = view => {\n    let state = view.state.field(searchState, false);\n    if (!state || !state.panel)\n        return false;\n    let panel = getPanel(view, createSearchPanel);\n    if (panel && panel.dom.contains(view.root.activeElement))\n        view.focus();\n    view.dispatch({ effects: togglePanel.of(false) });\n    return true;\n};\n/**\nDefault search-related key bindings.\n\n - Mod-f: [`openSearchPanel`](https://codemirror.net/6/docs/ref/#search.openSearchPanel)\n - F3, Mod-g: [`findNext`](https://codemirror.net/6/docs/ref/#search.findNext)\n - Shift-F3, Shift-Mod-g: [`findPrevious`](https://codemirror.net/6/docs/ref/#search.findPrevious)\n - Mod-Alt-g: [`gotoLine`](https://codemirror.net/6/docs/ref/#search.gotoLine)\n - Mod-d: [`selectNextOccurrence`](https://codemirror.net/6/docs/ref/#search.selectNextOccurrence)\n*/\nconst searchKeymap = [\n    { key: \"Mod-f\", run: openSearchPanel, scope: \"editor search-panel\" },\n    { key: \"F3\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Mod-g\", run: findNext, shift: findPrevious, scope: \"editor search-panel\", preventDefault: true },\n    { key: \"Escape\", run: closeSearchPanel, scope: \"editor search-panel\" },\n    { key: \"Mod-Shift-l\", run: selectSelectionMatches },\n    { key: \"Mod-Alt-g\", run: gotoLine },\n    { key: \"Mod-d\", run: selectNextOccurrence, preventDefault: true },\n];\nclass SearchPanel {\n    constructor(view) {\n        this.view = view;\n        let query = this.query = view.state.field(searchState).query.spec;\n        this.commit = this.commit.bind(this);\n        this.searchField = elt(\"input\", {\n            value: query.search,\n            placeholder: phrase(view, \"Find\"),\n            \"aria-label\": phrase(view, \"Find\"),\n            class: \"cm-textfield\",\n            name: \"search\",\n            form: \"\",\n            \"main-field\": \"true\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.replaceField = elt(\"input\", {\n            value: query.replace,\n            placeholder: phrase(view, \"Replace\"),\n            \"aria-label\": phrase(view, \"Replace\"),\n            class: \"cm-textfield\",\n            name: \"replace\",\n            form: \"\",\n            onchange: this.commit,\n            onkeyup: this.commit\n        });\n        this.caseField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"case\",\n            form: \"\",\n            checked: query.caseSensitive,\n            onchange: this.commit\n        });\n        this.reField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"re\",\n            form: \"\",\n            checked: query.regexp,\n            onchange: this.commit\n        });\n        this.wordField = elt(\"input\", {\n            type: \"checkbox\",\n            name: \"word\",\n            form: \"\",\n            checked: query.wholeWord,\n            onchange: this.commit\n        });\n        function button(name, onclick, content) {\n            return elt(\"button\", { class: \"cm-button\", name, onclick, type: \"button\" }, content);\n        }\n        this.dom = elt(\"div\", { onkeydown: (e) => this.keydown(e), class: \"cm-search\" }, [\n            this.searchField,\n            button(\"next\", () => findNext(view), [phrase(view, \"next\")]),\n            button(\"prev\", () => findPrevious(view), [phrase(view, \"previous\")]),\n            button(\"select\", () => selectMatches(view), [phrase(view, \"all\")]),\n            elt(\"label\", null, [this.caseField, phrase(view, \"match case\")]),\n            elt(\"label\", null, [this.reField, phrase(view, \"regexp\")]),\n            elt(\"label\", null, [this.wordField, phrase(view, \"by word\")]),\n            ...view.state.readOnly ? [] : [\n                elt(\"br\"),\n                this.replaceField,\n                button(\"replace\", () => replaceNext(view), [phrase(view, \"replace\")]),\n                button(\"replaceAll\", () => replaceAll(view), [phrase(view, \"replace all\")])\n            ],\n            elt(\"button\", {\n                name: \"close\",\n                onclick: () => closeSearchPanel(view),\n                \"aria-label\": phrase(view, \"close\"),\n                type: \"button\"\n            }, [\"×\"])\n        ]);\n    }\n    commit() {\n        let query = new SearchQuery({\n            search: this.searchField.value,\n            caseSensitive: this.caseField.checked,\n            regexp: this.reField.checked,\n            wholeWord: this.wordField.checked,\n            replace: this.replaceField.value,\n        });\n        if (!query.eq(this.query)) {\n            this.query = query;\n            this.view.dispatch({ effects: setSearchQuery.of(query) });\n        }\n    }\n    keydown(e) {\n        if (runScopeHandlers(this.view, e, \"search-panel\")) {\n            e.preventDefault();\n        }\n        else if (e.keyCode == 13 && e.target == this.searchField) {\n            e.preventDefault();\n            (e.shiftKey ? findPrevious : findNext)(this.view);\n        }\n        else if (e.keyCode == 13 && e.target == this.replaceField) {\n            e.preventDefault();\n            replaceNext(this.view);\n        }\n    }\n    update(update) {\n        for (let tr of update.transactions)\n            for (let effect of tr.effects) {\n                if (effect.is(setSearchQuery) && !effect.value.eq(this.query))\n                    this.setQuery(effect.value);\n            }\n    }\n    setQuery(query) {\n        this.query = query;\n        this.searchField.value = query.search;\n        this.replaceField.value = query.replace;\n        this.caseField.checked = query.caseSensitive;\n        this.reField.checked = query.regexp;\n        this.wordField.checked = query.wholeWord;\n    }\n    mount() {\n        this.searchField.select();\n    }\n    get pos() { return 80; }\n    get top() { return this.view.state.facet(searchConfigFacet).top; }\n}\nfunction phrase(view, phrase) { return view.state.phrase(phrase); }\nconst AnnounceMargin = 30;\nconst Break = /[\\s\\.,:;?!]/;\nfunction announceMatch(view, { from, to }) {\n    let line = view.state.doc.lineAt(from), lineEnd = view.state.doc.lineAt(to).to;\n    let start = Math.max(line.from, from - AnnounceMargin), end = Math.min(lineEnd, to + AnnounceMargin);\n    let text = view.state.sliceDoc(start, end);\n    if (start != line.from) {\n        for (let i = 0; i < AnnounceMargin; i++)\n            if (!Break.test(text[i + 1]) && Break.test(text[i])) {\n                text = text.slice(i);\n                break;\n            }\n    }\n    if (end != lineEnd) {\n        for (let i = text.length - 1; i > text.length - AnnounceMargin; i--)\n            if (!Break.test(text[i - 1]) && Break.test(text[i])) {\n                text = text.slice(0, i);\n                break;\n            }\n    }\n    return EditorView.announce.of(`${view.state.phrase(\"current match\")}. ${text} ${view.state.phrase(\"on line\")} ${line.number}.`);\n}\nconst baseTheme = /*@__PURE__*/EditorView.baseTheme({\n    \".cm-panel.cm-search\": {\n        padding: \"2px 6px 4px\",\n        position: \"relative\",\n        \"& [name=close]\": {\n            position: \"absolute\",\n            top: \"0\",\n            right: \"4px\",\n            backgroundColor: \"inherit\",\n            border: \"none\",\n            font: \"inherit\",\n            padding: 0,\n            margin: 0\n        },\n        \"& input, & button, & label\": {\n            margin: \".2em .6em .2em 0\"\n        },\n        \"& input[type=checkbox]\": {\n            marginRight: \".2em\"\n        },\n        \"& label\": {\n            fontSize: \"80%\",\n            whiteSpace: \"pre\"\n        }\n    },\n    \"&light .cm-searchMatch\": { backgroundColor: \"#ffff0054\" },\n    \"&dark .cm-searchMatch\": { backgroundColor: \"#00ffff8a\" },\n    \"&light .cm-searchMatch-selected\": { backgroundColor: \"#ff6a0054\" },\n    \"&dark .cm-searchMatch-selected\": { backgroundColor: \"#ff00ff8a\" }\n});\nconst searchExtensions = [\n    searchState,\n    /*@__PURE__*/Prec.low(searchHighlighter),\n    baseTheme\n];\n\nexport { RegExpCursor, SearchCursor, SearchQuery, closeSearchPanel, findNext, findPrevious, getSearchQuery, gotoLine, highlightSelectionMatches, openSearchPanel, replaceAll, replaceNext, search, searchKeymap, searchPanelOpen, selectMatches, selectNextOccurrence, selectSelectionMatches, setSearchQuery };\n"], "names": [], "sourceRoot": ""}