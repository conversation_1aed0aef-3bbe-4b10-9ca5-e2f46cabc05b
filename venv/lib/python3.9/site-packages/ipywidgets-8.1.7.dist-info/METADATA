Metadata-Version: 2.4
Name: ipywidgets
Version: 8.1.7
Summary: Jupyter interactive widgets
Home-page: http://jupyter.org
Author: Jupyter Development Team
Author-email: <EMAIL>
License: BSD 3-Clause License
Keywords: Interactive,Interpreter,Shell,Web,ipython,widgets,Jupyter
Platform: Linux
Platform: Mac OS X
Platform: Windows
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: Intended Audience :: Science/Research
Classifier: License :: OSI Approved :: BSD License
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Framework :: Jupyter
Requires-Python: >=3.7
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: comm>=0.1.3
Requires-Dist: ipython>=6.1.0
Requires-Dist: traitlets>=4.3.1
Requires-Dist: widgetsnbextension~=4.0.14
Requires-Dist: jupyterlab_widgets~=3.0.15
Provides-Extra: test
Requires-Dist: jsonschema; extra == "test"
Requires-Dist: ipykernel; extra == "test"
Requires-Dist: pytest>=3.6.0; extra == "test"
Requires-Dist: pytest-cov; extra == "test"
Requires-Dist: pytz; extra == "test"
Dynamic: license-file

# ipywidgets: Interactive HTML Widgets

**ipywidgets**, also known as jupyter-widgets or simply widgets, are
[interactive HTML widgets](https://github.com/jupyter-widgets/ipywidgets/blob/main/docs/source/examples/Index.ipynb)
for Jupyter notebooks and the IPython kernel.

This package contains the python implementation of the core interactive widgets bundled in ipywidgets.

## Core Interactive Widgets

The fundamental widgets provided by this library are called core interactive
widgets. A [demonstration notebook](https://github.com/jupyter-widgets/ipywidgets/blob/main/docs/source/examples/Index.ipynb)
provides an overview of the core interactive widgets, including:

- sliders
- progress bars
- text boxes
- toggle buttons and checkboxes
- display areas
- and more

For more information, see the main [documentation](https://github.com/jupyter-widgets/ipywidgets#readme).
